-- Migration: Finalize Dumpster Ordering Flow
-- Description: Ensures proper database relationships and adds functions for size-based grouping

-- Ensure dumpsters table has proper size_id relationship
DO $$
BEGIN
    -- Check if size_id column exists and has proper foreign key
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'dumpsters' AND column_name = 'size_id'
    ) THEN
        ALTER TABLE dumpsters ADD COLUMN size_id UUID REFERENCES dumpster_sizes(id);
    END IF;
END $$;

-- Ensure dumpster_sizes table has proper multilingual support
DO $$
BEGIN
    -- Add Arabic name columns if they don't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'dumpster_sizes' AND column_name = 'name_ar'
    ) THEN
        ALTER TABLE dumpster_sizes ADD COLUMN name_ar TEXT;
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'dumpster_sizes' AND column_name = 'description_ar'
    ) THEN
        ALTER TABLE dumpster_sizes ADD COLUMN description_ar TEXT;
    END IF;
    
    -- Rename 'name' to 'name_en' for consistency if needed
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'dumpster_sizes' AND column_name = 'name'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'dumpster_sizes' AND column_name = 'name_en'
    ) THEN
        ALTER TABLE dumpster_sizes RENAME COLUMN name TO name_en;
    END IF;
    
    -- Rename 'description' to 'description_en' for consistency if needed
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'dumpster_sizes' AND column_name = 'description'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'dumpster_sizes' AND column_name = 'description_en'
    ) THEN
        ALTER TABLE dumpster_sizes RENAME COLUMN description TO description_en;
    END IF;
END $$;

-- Create or update function to get dumpster size groups
CREATE OR REPLACE FUNCTION get_dumpster_size_groups(
    p_availability BOOLEAN DEFAULT NULL,
    p_city_id TEXT DEFAULT NULL,
    p_search_term TEXT DEFAULT NULL,
    p_waste_type_ids UUID[] DEFAULT NULL,
    p_min_price DECIMAL DEFAULT NULL,
    p_max_price DECIMAL DEFAULT NULL
)
RETURNS TABLE (
    size_id UUID,
    size_name_en TEXT,
    size_name_ar TEXT,
    capacity NUMERIC,
    dumpster_count BIGINT,
    partner_count BIGINT,
    min_price NUMERIC,
    max_price NUMERIC,
    average_rating NUMERIC,
    total_reviews BIGINT,
    image_url TEXT,
    is_available BOOLEAN,
    sample_dumpsters JSONB
) 
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    WITH filtered_dumpsters AS (
        SELECT 
            d.id,
            d.name_en,
            d.name_ar,
            d.image_url,
            d.price_per_load,
            d.rating,
            d.review_count,
            d.is_available,
            d.size_id,
            d.partner_id,
            ds.name_en as size_name_en,
            ds.name_ar as size_name_ar,
            ds.volume_cubic_yards as capacity
        FROM dumpsters d
        LEFT JOIN dumpster_sizes ds ON d.size_id = ds.id
        WHERE 
            (p_availability IS NULL OR d.is_available = p_availability)
            AND (p_search_term IS NULL OR 
                 d.name_en ILIKE '%' || p_search_term || '%' OR 
                 d.name_ar ILIKE '%' || p_search_term || '%')
            AND (p_min_price IS NULL OR d.price_per_load >= p_min_price)
            AND (p_max_price IS NULL OR d.price_per_load <= p_max_price)
            AND d.size_id IS NOT NULL
    ),
    size_groups AS (
        SELECT 
            fd.size_id,
            fd.size_name_en,
            fd.size_name_ar,
            fd.capacity,
            COUNT(fd.id) as dumpster_count,
            COUNT(DISTINCT fd.partner_id) as partner_count,
            MIN(fd.price_per_load) as min_price,
            MAX(fd.price_per_load) as max_price,
            AVG(fd.rating) as average_rating,
            SUM(fd.review_count) as total_reviews,
            (ARRAY_AGG(fd.image_url ORDER BY fd.price_per_load))[1] as image_url,
            BOOL_OR(fd.is_available) as is_available,
            JSONB_AGG(
                JSONB_BUILD_OBJECT(
                    'id', fd.id,
                    'nameEn', fd.name_en,
                    'nameAr', fd.name_ar,
                    'imageUrl', fd.image_url,
                    'pricePerLoad', fd.price_per_load,
                    'rating', fd.rating,
                    'reviewCount', fd.review_count,
                    'isAvailable', fd.is_available
                ) ORDER BY fd.price_per_load LIMIT 3
            ) as sample_dumpsters
        FROM filtered_dumpsters fd
        GROUP BY fd.size_id, fd.size_name_en, fd.size_name_ar, fd.capacity
    )
    SELECT 
        sg.size_id,
        sg.size_name_en,
        sg.size_name_ar,
        sg.capacity,
        sg.dumpster_count,
        sg.partner_count,
        sg.min_price,
        sg.max_price,
        sg.average_rating,
        sg.total_reviews,
        sg.image_url,
        sg.is_available,
        sg.sample_dumpsters
    FROM size_groups sg
    ORDER BY sg.capacity ASC;
END;
$$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_dumpsters_size_id ON dumpsters(size_id);
CREATE INDEX IF NOT EXISTS idx_dumpsters_is_available ON dumpsters(is_available);
CREATE INDEX IF NOT EXISTS idx_dumpsters_price_per_load ON dumpsters(price_per_load);
CREATE INDEX IF NOT EXISTS idx_dumpster_sizes_capacity ON dumpster_sizes(volume_cubic_yards);

-- Update sample data if dumpster_sizes table is empty
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM dumpster_sizes LIMIT 1) THEN
        INSERT INTO dumpster_sizes (id, name_en, name_ar, volume_cubic_yards, max_weight_pounds, length, width, height, description_en, description_ar)
        VALUES 
            (gen_random_uuid(), '5 Yard Container', 'حاوية 5 ياردة', 5, 2000, 8, 6, 3, 'Perfect for small projects and cleanouts', 'مثالية للمشاريع الصغيرة والتنظيف'),
            (gen_random_uuid(), '10 Yard Container', 'حاوية 10 ياردة', 10, 3000, 12, 8, 4, 'Great for medium-sized renovation projects', 'رائعة لمشاريع التجديد متوسطة الحجم'),
            (gen_random_uuid(), '15 Yard Container', 'حاوية 15 ياردة', 15, 4000, 14, 8, 4.5, 'Ideal for large home renovations', 'مثالية لتجديدات المنازل الكبيرة'),
            (gen_random_uuid(), '20 Yard Container', 'حاوية 20 ياردة', 20, 5000, 16, 8, 5, 'Perfect for construction projects', 'مثالية لمشاريع البناء'),
            (gen_random_uuid(), '30 Yard Container', 'حاوية 30 ياردة', 30, 7000, 20, 8, 6, 'Great for large construction and commercial projects', 'رائعة للبناء الكبير والمشاريع التجارية'),
            (gen_random_uuid(), '40 Yard Container', 'حاوية 40 ياردة', 40, 8000, 22, 8, 8, 'Ideal for major construction and demolition', 'مثالية للبناء الكبير والهدم')
        ON CONFLICT (name_en) DO NOTHING;
    END IF;
END $$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_dumpster_size_groups TO authenticated;
GRANT EXECUTE ON FUNCTION get_dumpster_size_groups TO anon;

-- Add comment
COMMENT ON FUNCTION get_dumpster_size_groups IS 'Returns dumpster inventory grouped by size with aggregated statistics for the home screen display';
