import React, { useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, Dimensions, ScrollView, Image, ActivityIndicator } from 'react-native';
import { styled } from 'nativewind';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { NewRTLText, NewRTLView } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import RBSheet from 'react-native-raw-bottom-sheet';
import { useTranslation } from 'react-i18next';
import { DumpsterSizeGroup } from '@/types/v2/dumpster';
import { usePartnerOffers } from '@/hooks/usePartnerOffers';
import { PartnerOffer } from '@/components/PartnerOfferList';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledScrollView = styled(ScrollView);

interface DumpsterSizeGroupBottomSheetProps {
  sheetRef: React.RefObject<any>;
  sizeGroup: DumpsterSizeGroup | null;
  onPartnerSelect: (offer: PartnerOffer) => void;
  onClose: () => void;
}

/**
 * DumpsterSizeGroupBottomSheet - A bottom sheet component for displaying partner offers for a dumpster size group
 *
 * Features:
 * - Displays size group information (name, capacity, price range)
 * - Shows list of partner offers sorted by price and rating
 * - Supports RTL layouts and dark mode
 * - Follows the discover-v3.tsx pattern for partner listings
 */
export default function DumpsterSizeGroupBottomSheet({
  sheetRef,
  sizeGroup,
  onPartnerSelect,
  onClose,
}: DumpsterSizeGroupBottomSheetProps) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const screenHeight = Dimensions.get('window').height;

  // Get partner offers for the first dumpster in the group (as a representative)
  const representativeDumpsterId = sizeGroup?.sampleDumpsters[0]?.id || null;
  const basePrice = sizeGroup?.minPrice || 0;

  const {
    data: partnerOffers = [],
    isLoading: isLoadingOffers
  } = usePartnerOffers(representativeDumpsterId, basePrice);

  // Handle partner offer selection
  const handleOfferSelect = (offer: PartnerOffer) => {
    onPartnerSelect(offer);
    sheetRef.current?.close();
  };

  if (!sizeGroup) return null;

  return (
    <RBSheet
      ref={sheetRef}
      closeOnDragDown={true}
      closeOnPressMask={true}
      height={screenHeight * 0.9}
      customStyles={{
        wrapper: {
          backgroundColor: 'rgba(0,0,0,0.5)',
        },
        container: {
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light,
        },
        draggableIcon: {
          backgroundColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
          width: 60,
          height: 6,
        },
      }}
      animationType="slide"
    >
      <StyledView className="flex-1">
        {/* Header */}
        <StyledView className="px-4 pt-4 pb-2">
          <StyledView className="flex-row justify-between items-center">
            <StyledText 
              className="text-xl font-bold"
              style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}
            >
              {isRTL ? sizeGroup.size.nameAr : sizeGroup.size.nameEn}
            </StyledText>
            <StyledTouchableOpacity
              onPress={onClose}
              className="w-8 h-8 rounded-full justify-center items-center"
              style={{ backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }}
            >
              <MaterialIcons name="close" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
            </StyledTouchableOpacity>
          </StyledView>
        </StyledView>

        {/* Size Group Info */}
        <StyledView className="px-4 pb-4">
          <StyledView className="flex-row items-center mb-3">
            <Image
              source={{ uri: sizeGroup.imageUrl }}
              style={{ width: 60, height: 40, borderRadius: 8 }}
              resizeMode="cover"
            />
            <StyledView className="ml-3 flex-1">
              <StyledText 
                className="text-sm font-medium"
                style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}
              >
                {sizeGroup.size.capacity} {t('cubic_yards')} • {sizeGroup.dumpsterCount} {t('available')}
              </StyledText>
              <StyledText 
                className="text-lg font-bold"
                style={{ color: isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600] }}
              >
                ﷼{sizeGroup.minPrice.toLocaleString()} - ﷼{sizeGroup.maxPrice.toLocaleString()}
              </StyledText>
            </StyledView>
          </StyledView>

          {/* Best For Tags */}
          <StyledView className="flex-row flex-wrap">
            {sizeGroup.bestFor.map((tag, index) => (
              <StyledView
                key={index}
                className="bg-blue-100 dark:bg-blue-900 rounded-full px-3 py-1 mr-2 mb-2"
              >
                <StyledText 
                  className="text-xs font-medium"
                  style={{ color: isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[700] }}
                >
                  {tag}
                </StyledText>
              </StyledView>
            ))}
          </StyledView>
        </StyledView>

        {/* Partner Offers Section */}
        <StyledView className="flex-1 px-4">
          <StyledText 
            className="text-lg font-bold mb-4"
            style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}
          >
            {t('partner_offers')} ({sizeGroup.partnerCount})
          </StyledText>

          {isLoadingOffers ? (
            <StyledView className="py-8 items-center">
              <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
              <StyledText 
                className="text-center mt-2"
                style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}
              >
                {t('loading_partner_offers')}
              </StyledText>
            </StyledView>
          ) : partnerOffers.length === 0 ? (
            <StyledView className="py-8 items-center">
              <MaterialIcons name="info" size={24} color={colors.textColors.secondary.light} />
              <StyledText 
                className="text-center mt-2"
                style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}
              >
                {t('no_partner_offers_available')}
              </StyledText>
            </StyledView>
          ) : (
            <StyledScrollView showsVerticalScrollIndicator={false}>
              {partnerOffers.map((offer) => (
                <StyledTouchableOpacity
                  key={offer.id}
                  className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-3"
                  style={{
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.1,
                    shadowRadius: 3,
                    elevation: 2,
                  }}
                  onPress={() => handleOfferSelect(offer)}
                  activeOpacity={0.7}
                >
                  <StyledView className="flex-row items-center">
                    {/* Partner Image */}
                    <StyledView className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700">
                      {offer.partner.profile?.avatar_url ? (
                        <Image
                          source={{ uri: offer.partner.profile.avatar_url }}
                          style={{ width: '100%', height: '100%' }}
                          resizeMode="cover"
                        />
                      ) : (
                        <StyledView className="w-full h-full justify-center items-center">
                          <MaterialIcons name="business" size={20} color={colors.textColors.secondary.light} />
                        </StyledView>
                      )}
                    </StyledView>

                    {/* Partner Info */}
                    <StyledView className="ml-3 flex-1">
                      <StyledText 
                        className="text-base font-medium"
                        style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}
                      >
                        {offer.partner.company_name}
                      </StyledText>

                      {offer.partner.rating > 0 && (
                        <StyledView className="flex-row items-center mt-1">
                          <MaterialIcons name="star" size={14} color="#FFD700" />
                          <StyledText 
                            className="text-sm ml-1"
                            style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}
                          >
                            {offer.partner.rating.toFixed(1)}
                          </StyledText>
                        </StyledView>
                      )}
                    </StyledView>

                    {/* Price */}
                    <StyledView 
                      className="px-3 py-2 rounded-lg"
                      style={{ backgroundColor: isDarkMode ? colors.brandColors.primary[900] : colors.brandColors.primary[50] }}
                    >
                      <StyledText 
                        className="font-bold"
                        style={{ color: isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600] }}
                      >
                        ﷼{offer.price.toLocaleString()}
                      </StyledText>
                    </StyledView>
                  </StyledView>
                </StyledTouchableOpacity>
              ))}
            </StyledScrollView>
          )}
        </StyledView>
      </StyledView>
    </RBSheet>
  );
}
