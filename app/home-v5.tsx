import React, { useEffect, useState, useRef } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, Dimensions, Image, Pressable } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import AndroidSafeAreaView from '@/components/ui/AndroidSafeAreaView';
import { styled } from 'nativewind';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Feather } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  interpolate,
  Extrapolation
} from 'react-native-reanimated';

// Hooks
import { useAuth, GlobalUserIdentifier } from '@/context/AuthContext';
import { useProfile } from '@/hooks/useProfile';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { useDumpsters } from '@/hooks/v2/useDumpsters';

// Components
import { NewRTLText, NewRTLView } from '@/components/rtl/new-index';
import HeroCard from '@/components/home/<USER>';
import MarketingCarousel, { BannerItem } from '@/components/home/<USER>';
import StickySearchHeader, { FilterOption } from '@/components/molecules/StickySearchHeader';
import DumpsterGroupCard from '@/components/home/<USER>';
import CitySelector, { City, SAUDI_CITIES } from '@/components/home/<USER>';
import CityBottomSheet from '@/components/home/<USER>';

// Utils
import { safeTranslate } from '@/utils/i18nHelpers';
import { getUserName } from '@/utils/user';
import * as colors from '@/theme/colors';

// Types
import { DumpsterFilters } from '@/types/v2/dumpster';

const StyledView = styled(View);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledAndroidSafeAreaView = styled(AndroidSafeAreaView);
const StyledAnimatedView = styled(Animated.View);

// Mock data for hero card
const HERO_DATA = {
  imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/marketing/hero-banner-1.jpg',
  headline: 'Premium Dumpsters',
  subtitle: 'Find the perfect dumpster for your project',
};

// Mock banner data - in production, this would come from an API
const MARKETING_BANNERS: BannerItem[] = [
  {
    id: '1',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner.png',
    title: 'Spring Cleaning Special',
    subtitle: 'Get 15% off your next dumpster rental',
    actionUrl: '/promotions/spring-cleaning',
  },
  {
    id: '2',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner-en.png',
    title: 'New Eco-Friendly Options',
    subtitle: 'Sustainable waste management solutions',
    actionUrl: '/eco-friendly',
  },
  {
    id: '3',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner-ar.png',
    title: 'Same-Day Delivery',
    subtitle: 'For orders placed before noon',
    actionUrl: '/same-day-delivery',
  },
];

// Mock data for filter options
const PROJECT_TYPES: FilterOption[] = [
  { id: 'construction', label: 'Construction' },
  { id: 'renovation', label: 'Renovation' },
  { id: 'landscaping', label: 'Landscaping' },
  { id: 'cleaning', label: 'Cleaning' },
];

const COUNT_OPTIONS: FilterOption[] = [
  { id: 'small', label: 'Small (1-5 yards)' },
  { id: 'medium', label: 'Medium (6-15 yards)' },
  { id: 'large', label: 'Large (16+ yards)' },
];

/**
 * HomeV5Screen - An improved home screen with better touch handling
 *
 * Features:
 * - Uses ScrollView instead of FlatList for better touch event handling
 * - Compact app bar with document icon and avatar
 * - Personalized greeting and city selector
 * - Sticky search/filter bar
 * - Grid layout for dumpster tiles
 * - Supports RTL layouts and dark mode
 */
export default function HomeV5Screen() {
  const router = useRouter();
  const { t, i18n } = useTranslation();
  const { session } = useAuth();
  const { profile } = useProfile(GlobalUserIdentifier.profileId || '');
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const screenWidth = Dimensions.get('window').width;

  // Add missing translations
  useEffect(() => {
    // Add translations if they don't exist
    if (!i18n.exists('select_city')) {
      i18n.addResources('en', 'translation', {
        select_city: 'Select City',
        search_cities: 'Search cities',
        current_location: 'Current Location',
      });

      i18n.addResources('ar', 'translation', {
        select_city: 'اختر المدينة',
        search_cities: 'ابحث عن المدن',
        current_location: 'الموقع الحالي',
      });
    }
  }, []);

  // Refs for sticky header
  const scrollY = useSharedValue(0);
  const headerHeight = 340; // Height of top nav + greeting + marketing carousel

  // Refs
  const cityBottomSheetRef = useRef<any>(null);

  // State
  const [userName, setUserName] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [selectedProjectType, setSelectedProjectType] = useState<string | null>(null);
  const [selectedCount, setSelectedCount] = useState<string | null>(null);
  const [selectedCity, setSelectedCity] = useState<City>(SAUDI_CITIES[0]); // Default to Riyadh
  const [filters, setFilters] = useState<DumpsterFilters>({
    availability: true,
  });

  const handleBannerPress = (banner: BannerItem) => {
    if (banner.actionUrl) {
      router.push(banner.actionUrl);
    }
  };

  // Fetch dumpsters with V2 API
  const {
    data: dumpsters,
    isLoading: isLoadingDumpsters
  } = useDumpsters(filters);

  // Get user name
  useEffect(() => {
    if (profile?.id) {
      const fetchUserName = async () => {
        const name = await getUserName();
        if (name) {
          setUserName(name.split(' ')[0]);
        } else {
          setUserName('User');
        }
      };
      fetchUserName();
    }
  }, [profile]);

  // Handle scroll events
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  // Animated styles for app bar
  const appBarStyle = useAnimatedStyle(() => {
    const elevation = interpolate(
      scrollY.value,
      [headerHeight - 20, headerHeight],
      [0, 4],
      Extrapolation.CLAMP
    );

    return {
      shadowOpacity: interpolate(
        scrollY.value,
        [headerHeight - 20, headerHeight],
        [0, 0.2],
        Extrapolation.CLAMP
      ),
      elevation,
      zIndex: 1030,
      backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light,
    };
  });

  // Get time of day for greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return safeTranslate(t, 'home.greeting.morning', 'Good morning');
    if (hour < 18) return safeTranslate(t, 'home.greeting.afternoon', 'Good afternoon');
    return safeTranslate(t, 'home.greeting.evening', 'Good evening');
  };

  // Navigation handlers
  const openOrderHistory = () => {
    router.push('/orders');
  };

  const openProfile = () => {
    router.push('/profile');
  };

  const handleDumpsterPress = (dumpster: any) => {
    router.push({
      pathname: '/dumpster-details',
      params: { id: dumpster.id }
    });
  };

  const handleFilterPress = () => {
    // Open filter bottom sheet
    console.log('Open filter bottom sheet');
  };

  const handleSortPress = () => {
    // Open sort options
    console.log('Open sort options');
  };

  const handleCityPress = () => {
    // Open city selector bottom sheet
    console.log('Opening city selector bottom sheet');
    if (cityBottomSheetRef.current) {
      cityBottomSheetRef.current.open();
    }
  };

  // Render dumpster group card
  const renderDumpsterTile = ({ item }: { item: any }) => {
    // Get best-for tags based on size ID
    const bestFor = item.sizeId ?
      ['size-1', 'size-2', 'size-3', 'size-4'].includes(item.sizeId) ?
        ['Small Projects', 'Residential', 'Medium Projects', 'Renovation', 'Large Projects', 'Construction', 'Commercial', 'Heavy Debris'].slice(
          item.sizeId === 'size-1' ? 0 :
          item.sizeId === 'size-2' ? 2 :
          item.sizeId === 'size-3' ? 4 : 6,
          item.sizeId === 'size-1' ? 2 :
          item.sizeId === 'size-2' ? 4 :
          item.sizeId === 'size-3' ? 6 : 8
        ) : []
      : [];

    return (
      <DumpsterGroupCard
        dumpster={item}
        onPress={() => handleDumpsterPress(item)}
        bestFor={bestFor}
        availableCount={Math.floor(Math.random() * 10) + 1}
      />
    );
  };

  return (
    <StyledAndroidSafeAreaView className="flex-1" style={{ backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }} >
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      {/* Top Navigation */}
      <StyledView className="flex-row justify-between items-center px-5 py-2">
        <StyledTouchableOpacity
          onPress={openOrderHistory}
          className="w-10 h-10 rounded-full justify-center items-center" style={{
            backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          }}
        >
          <Feather name="file-text" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
        </StyledTouchableOpacity>

        <StyledTouchableOpacity
          onPress={openProfile}
          className="w-10 h-10 rounded-full justify-center items-center" style={{
            backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          }}
        >
          <Feather name="user" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
        </StyledTouchableOpacity>
      </StyledView>

      {/* Top Search Header (initially hidden) */}
      <StickySearchHeader
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        onFilterPress={handleFilterPress}
        onSortPress={handleSortPress}
        projectTypes={PROJECT_TYPES}
        selectedProjectType={selectedProjectType}
        onProjectTypeChange={setSelectedProjectType}
        countOptions={COUNT_OPTIONS}
        selectedCount={selectedCount}
        onCountChange={setSelectedCount}
        scrollY={scrollY}
        stickyThreshold={headerHeight}
        testID="top-search-header"
        isTopHeader={true}
      />

      {/* Main Content - Using Animated ScrollView for better touch handling */}
      <Animated.ScrollView
        showsVerticalScrollIndicator={false}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        contentContainerStyle={{ paddingBottom: 20 }}
      >
        {/* Header Content */}
        <View style={{ paddingHorizontal: 16, paddingTop: 8 }}>
          {/* Greeting and City Selector */}
          <View style={{
            marginVertical: 12,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
          }}>
            {/* Greeting Text */}
            <View style={{ flex: 1 }}>
              <NewRTLText
                style={{
                  fontSize: 24,
                  fontWeight: '600',
                  color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                  textAlign: isRTL ? 'right' : 'left',
                }}
              >
                {getGreeting()},
              </NewRTLText>
              <NewRTLText
                style={{
                  fontSize: 24,
                  fontWeight: '400',
                  color: isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600],
                  textAlign: isRTL ? 'right' : 'left',
                  marginBottom: 4,
                }}
              >
                {userName || 'User'}
              </NewRTLText>
            </View>

            {/* City Selector */}
            <TouchableOpacity
              onPress={handleCityPress}
              activeOpacity={0.7}
              style={{ marginTop: 8 }}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <CitySelector
                selectedCity={selectedCity}
                onPress={handleCityPress}
              />
            </TouchableOpacity>
          </View>

          {/* Marketing Carousel */}
          <MarketingCarousel
            banners={MARKETING_BANNERS}
            onBannerPress={handleBannerPress}
          />

          {/* Content Search Header */}
          <View style={{ marginTop: 8 }}>
            <StickySearchHeader
              searchValue={searchValue}
              onSearchChange={setSearchValue}
              onFilterPress={handleFilterPress}
              onSortPress={handleSortPress}
              projectTypes={PROJECT_TYPES}
              selectedProjectType={selectedProjectType}
              onProjectTypeChange={setSelectedProjectType}
              countOptions={COUNT_OPTIONS}
              selectedCount={selectedCount}
              onCountChange={setSelectedCount}
              scrollY={scrollY}
              stickyThreshold={headerHeight}
              testID="content-search-header"
              isTopHeader={false}
            />
          </View>
        </View>

        {/* Dumpster Grid */}
        <View style={{ paddingHorizontal: 12, marginTop: 16 }}>
          {isLoadingDumpsters ? (
            <View style={{ paddingVertical: 40, alignItems: 'center' }}>
              <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
            </View>
          ) : dumpsters && dumpsters.length > 0 ? (
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
              {dumpsters.map((item) => (
                <View key={item.id} style={{ width: screenWidth / 2 - 20, marginBottom: 16 }}>
                  {renderDumpsterTile({ item })}
                </View>
              ))}
            </View>
          ) : (
            <View style={{ paddingVertical: 40, alignItems: 'center' }}>
              <NewRTLText
                style={{
                  color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                  textAlign: 'center',
                }}
              >
                {safeTranslate(t, 'home.noDumpsters', 'No dumpsters available')}
              </NewRTLText>
            </View>
          )}
        </View>
      </Animated.ScrollView>

      {/* City Bottom Sheet */}
      <CityBottomSheet
        cities={SAUDI_CITIES}
        selectedCity={selectedCity}
        onSelectCity={setSelectedCity}
        sheetRef={cityBottomSheetRef}
      />

      {/* Bottom Input Button */}
      <View style={{
        position: 'absolute',
        bottom: 40,
        left: 20,
        right: 20,
        alignItems: 'center',
        width: '90%'
      }}>
        <Pressable
          onPress={() => router.push('/dumpster-selection')}
          style={{
            width: '100%',
            backgroundColor: isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[500],
            paddingVertical: 15,
            paddingHorizontal: 25,
            borderRadius: 100,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3
          }}
        >
          <View style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%'
          }}>
            <View style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: isDarkMode ? colors.brandColors.primary[100] : colors.brandColors.primary[900],
              justifyContent: 'center',
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 4,
              elevation: 4
            }}>
              <Feather name="mic" size={20} color={isDarkMode ? colors.textColors.light : colors.textColors.dark} />
            </View>
            <NewRTLText style={{
              flex: 1,
              fontSize: 16,
              color: isDarkMode ? colors.textColors.light : colors.brandColors.secondary[700],
              fontWeight: '500',
              marginLeft: isRTL ? 0 : 12,
              marginRight: isRTL ? 12 : 0,
              textAlign: isRTL ? 'right' : 'left'
            }}>
              {safeTranslate(t, 'home.inputPrompt', 'Tap to type')}
            </NewRTLText>
          </View>
        </Pressable>
      </View>
    </StyledAndroidSafeAreaView>

  );
}
