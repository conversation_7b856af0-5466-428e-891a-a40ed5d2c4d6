import React, { useState } from 'react';
import { View, Text, ScrollView, Image, StyleSheet, ActivityIndicator, Dimensions, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { IconButton } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import NewRTLText from '@/components/rtl/NewRTLText';

// Debug component focusing on currency image and slash icon
export default function DebugDumpsterDetailCurrency() {
  const { t } = useTranslation();
  const router = useRouter();
  const { isDarkMode } = useTheme();
  
  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Debug Currency',
          headerLeft: () => (
            <IconButton
              icon="arrow-left"
              size={24}
              onPress={() => router.back()}
              style={styles.headerIcon}
            />
          ),
        }}
      />
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.contentContainer}>
          {/* Title */}
          <NewRTLText style={styles.title}>
            {t('Testing Currency Elements')}
          </NewRTLText>
          
          {/* Testing currency image */}
          <View style={styles.testSection}>
            <NewRTLText style={styles.sectionTitle}>
              {t('Testing Saudi Riyal Image')}
            </NewRTLText>
            
            <View style={styles.row}>
              <Image 
                source={require('@/assets/images/currency/Saudi_Riyal_Symbol.png')} 
                style={{ width: 24, height: 24 }} 
                resizeMode="contain" 
              />
              <NewRTLText style={styles.priceText}>
                100
              </NewRTLText>
            </View>
          </View>
          
          {/* Testing price with slash */}
          <View style={styles.testSection}>
            <NewRTLText style={styles.sectionTitle}>
              {t('Testing Slash Icon')}
            </NewRTLText>
            
            <View style={styles.row}>
              <Image 
                source={require('@/assets/images/currency/Saudi_Riyal_Symbol.png')} 
                style={{ width: 24, height: 24 }} 
                resizeMode="contain" 
              />
              <NewRTLText style={styles.priceText}>
                100
              </NewRTLText>
              <MaterialCommunityIcons name="slash-forward" size={24} color="#6b7280" />
              <NewRTLText style={styles.unitText}>
                {t('day')}
              </NewRTLText>
            </View>
          </View>
          
          {/* Testing dimensions display */}
          <View style={styles.testSection}>
            <NewRTLText style={styles.sectionTitle}>
              {t('Testing Dimensions Display')}
            </NewRTLText>
            
            <View style={styles.row}>
              <NewRTLText style={styles.dimText}>
                10
              </NewRTLText>
              <NewRTLText style={styles.dimText}>
                {t('"')}
              </NewRTLText>
              <NewRTLText style={styles.dimText}>
                {t(' × ')}
              </NewRTLText>
              <NewRTLText style={styles.dimText}>
                20
              </NewRTLText>
              <NewRTLText style={styles.dimText}>
                {t('"')}
              </NewRTLText>
              <NewRTLText style={styles.dimText}>
                {t(' × ')}
              </NewRTLText>
              <NewRTLText style={styles.dimText}>
                30
              </NewRTLText>
              <NewRTLText style={styles.dimText}>
                {t('"')}
              </NewRTLText>
            </View>
          </View>
          
          {/* Button */}
          <TouchableOpacity 
            style={styles.button}
            onPress={() => router.back()}
          >
            <NewRTLText style={styles.buttonText}>
              {t('Go Back')}
            </NewRTLText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    alignItems: 'center',
  },
  headerIcon: {
    backgroundColor: '#e5e7eb',
    borderRadius: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 24,
    textAlign: 'center',
  },
  testSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    marginBottom: 16,
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4b5563',
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3b82f6',
    marginLeft: 4,
  },
  unitText: {
    fontSize: 16,
    color: '#6b7280',
    marginLeft: 4,
  },
  dimText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  button: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
}); 