import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import { styled } from 'nativewind';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledScrollView = styled(ScrollView);

export default function DebugMenuScreen() {
  const router = useRouter();

  const debugOptions = [
    {
      title: 'Basic NewRTLText Test',
      description: 'Test basic NewRTLText component rendering',
      route: '/dumpster-selection/debug-id',
      icon: 'text-fields'
    },
    {
      title: 'Standard Text Test',
      description: 'Test standard Text component rendering',
      route: '/dumpster-selection/debug-id-text',
      icon: 'text-snippet'
    },
    {
      title: 'Currency Symbol Test',
      description: 'Test Saudi Riyal symbol and slash icon rendering',
      route: '/dumpster-selection/debug-id-currency',
      icon: 'attach-money'
    },
    {
      title: 'SafeText Test',
      description: 'Test SafeText component with null/undefined handling',
      route: '/dumpster-selection/debug-id-safetext',
      icon: 'security'
    }
  ];

  return (
    <StyledScrollView className="flex-1 bg-gray-50">
      <Stack.Screen 
        options={{
          title: 'Debug Menu',
          headerShown: true
        }}
      />
      
      <StyledView className="p-4">
        <StyledText className="text-lg font-semibold text-gray-800 mb-4">
          Debug Views
        </StyledText>
        
        {debugOptions.map((option, index) => (
          <StyledTouchableOpacity
            key={index}
            onPress={() => router.push(option.route)}
            className="bg-white rounded-lg p-4 mb-3 shadow-sm"
          >
            <StyledView className="flex-row items-center">
              <MaterialIcons name={option.icon as any} size={24} color="#3b82f6" />
              <StyledView className="ml-3 flex-1">
                <StyledText className="text-base font-medium text-gray-800">
                  {option.title}
                </StyledText>
                <StyledText className="text-sm text-gray-600">
                  {option.description}
                </StyledText>
              </StyledView>
              <MaterialIcons name="chevron-right" size={24} color="#9ca3af" />
            </StyledView>
          </StyledTouchableOpacity>
        ))}
      </StyledView>
    </StyledScrollView>
  );
} 