import React, { useState } from 'react';
import { View, SafeAreaView, Alert, TouchableOpacity, Text, LogBox } from 'react-native';
import { styled } from 'nativewind';
import { NavigationDots } from './NavigationDots';
import { NavigationButtons } from './NavigationButtons';
import { OnboardingContent } from './OnboardingContent';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { backgroundColors, brandColors, textColors } from '@/theme/colors';
import * as Notifications from 'expo-notifications';
import * as Location from 'expo-location';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/context/AuthContext';

const StyledView = styled(View);
const StyledSafeAreaView = styled(SafeAreaView);

interface OnboardingScreenProps {
  onComplete: () => void;
}

export const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ onComplete }) => {
  const [currentScreen, setCurrentScreen] = useState(0);
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const { session, userIdentifier } = useAuth();
  LogBox.ignoreAllLogs(true);
  const handleSkip = () => {
    onComplete();
  };

  const requestNotificationPermission = async () => {
    const { status } = await Notifications.requestPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please enable notifications to receive updates about your dumpster orders.',
        [{ text: 'OK' }]
      );
    }
  };

  const requestLocationPermission = async () => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please enable location services to help us find nearby dumpster locations and save your addresses.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleNext = async () => {
    if (currentScreen === 0) {
      // Request notification permission at step 1 (before step 2)
      await requestNotificationPermission();
    } else if (currentScreen === 1) {
      // Request location permission at step 2 (before step 3)
      await requestLocationPermission();
    }

    if (currentScreen < 2) {
      setCurrentScreen(prev => prev + 1);
    } else {
      onComplete();
    }
  };

  const getOnboardingContent = () => {
    switch (currentScreen) {
      case 0:
        return {
          title: "Easy and safe way to rent dumpster for short and long term",
          image: "https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/onBoarding/onboarding-1.png"
        };
      case 1:
        return {
          title: "Stay updated with real-time notifications about your dumpster orders",
          image: "https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/onBoarding/onboarding-2.png"
        };
      case 2:
        return {
          title: "Share your location to find nearby dumpster locations and save addresses",
          image: "https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/onBoarding/onboarding-3.png"
        };
      default:
        return {
          title: "Easy and safe way to rent dumpster for short and long term",
          image: "https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/onBoarding/onboarding-1.png"
        };
    }
  };

  const content = getOnboardingContent();

  return (
    <StyledSafeAreaView 
      style={{ 
        backgroundColor: isDarkMode ? backgroundColors.main.dark : backgroundColors.main.light,
        flex: 1 , paddingTop: 40
      }}
    >
      <StyledView className="flex-1">
        <NavigationButtons onSkip={handleSkip} onNext={handleNext} />

        <OnboardingContent
          title={content.title}
          image={content.image}
        />

        <StyledView className="mb-12">
          <NavigationDots totalDots={3} activeDotIndex={currentScreen} />
        </StyledView>
      </StyledView>
    </StyledSafeAreaView>
  );
};

export default OnboardingScreen;
