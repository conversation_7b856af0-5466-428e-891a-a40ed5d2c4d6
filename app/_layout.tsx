import React, { useEffect, useState, useRef } from 'react';
import { View, ActivityIndicator, Text, Button, Platform, Alert, TouchableOpacity, AppState } from 'react-native';
import { Stack, useRouter, useSegments, usePathname } from 'expo-router';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { I18nextProvider } from 'react-i18next';
import { AuthProvider, useAuth } from '@/context/AuthContext';
import { ThemeProvider, useTheme } from '@/context/ThemeContext';
import i18n, { initializeLanguageSettings } from '@/i18n';
import * as Updates from 'expo-updates';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { DefaultTheme } from '@react-navigation/native';
import { ensureTranslationsLoaded } from '@/utils/i18nHelpers';
import { RTLProvider } from '@/components/rtl/new-index';
import { MigrationProvider } from '@/context/MigrationContext';
import StatusBarManager from '@/components/ui/StatusBarManager';
import { supabase } from '@/lib/supabase';
import { useTranslation } from 'react-i18next';
import * as colors from '@/theme/colors';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { setupNotificationListeners } from '@/hooks/useOrderRealtime';
import * as Notifications from 'expo-notifications';
import { RealtimeChannel } from '@supabase/supabase-js';

// Conditionally import background fetch registration
let registerBackgroundFetch: () => Promise<void>;
try {
  // Use dynamic import to avoid errors if module is not available
  if (Platform.OS !== 'web') {
    const orderRealtimeModule = require('@/hooks/useOrderRealtime');
    registerBackgroundFetch = orderRealtimeModule.registerBackgroundFetch;
  }
} catch (err) {
  console.warn('[_layout] Could not import background fetch registration:', err);
  registerBackgroundFetch = async () => {
    console.log('[_layout] Background fetch registration not available');
  };
}

const LANGUAGE_KEY = '@app:language';
const FORCE_RESTART_KEY = '@app:force_restart';
const RTL_RETRY_COUNT_KEY = '@app:rtl_retry_count';
const MAX_RTL_RETRIES = 2; // Must match the value in i18n.ts

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
});

// Update the theme to match the new design
const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#3b82f6', // Blue color
    accent: '#10b981',  // Green color
    background: '#f5f7fa',
    surface: '#ffffff',
    text: '#1f2937',
    placeholder: '#9ca3af',
  },
};

function AuthenticationGuard({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, loading, session } = useAuth();
  const [isVerifyingOtp, setIsVerifyingOtp] = useState<boolean>(false);
  const [isLoadingProfile, setIsLoadingProfile] = useState<boolean>(false);
  const [authCheckAttempts, setAuthCheckAttempts] = useState<number>(0);
  const [showingAlert, setShowingAlert] = useState<boolean>(false);
  const segments = useSegments();
  const router = useRouter();
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const preventLoginRedirect = useRef(false);
  const resetTapCount = useRef(0);
  const resetTimer = useRef<NodeJS.Timeout>();

  // Force clear all auth state function
  const forceClearAuthState = async () => {
    try {
      console.log('[_layout]FORCE CLEARING ALL AUTH STATE');
      await AsyncStorage.multiRemove([
        '@app:user_role',
        '@app:profile_id',
        '@app:otp_verification_in_progress',
        '@app:verification_timestamp',
        '@app:verifying_otp_in_progress',
        '@app:verifying_phone',
        '@app:last_auth_check',
        '@app:temp_user_type'
      ]);
      await supabase.auth.signOut({ scope: 'global' });
      setAuthCheckAttempts(0);
      setIsVerifyingOtp(false);
      setIsLoadingProfile(false);
      setShowingAlert(false);
      preventLoginRedirect.current = true;

      // Force reload the app
      router.replace('/(auth)/language-select');

      // Allow redirects again after a delay
      setTimeout(() => {
        preventLoginRedirect.current = false;
      }, 5000);
    } catch (err) {
      console.log('[_layout] Error in forceClearAuthState:', err);
    }
  };

  // Reset handler when logo is tapped multiple times
  const handleResetTap = () => {
    resetTapCount.current += 1;

    if (resetTapCount.current >= 5) {
      Alert.alert(
        'Emergency Reset',
        'Would you like to reset all authentication state to fix login issues?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Reset', onPress: forceClearAuthState }
        ]
      );
      resetTapCount.current = 0;
    }

    // Reset the counter after 3 seconds of inactivity
    clearTimeout(resetTimer.current);
    resetTimer.current = setTimeout(() => {
      resetTapCount.current = 0;
    }, 3000);
  };

  React.useEffect(() => {
    // Expose the reset handler to global object for access in other components
    if (typeof global !== 'undefined') {
      (global as any).__RESET_AUTH_STATE__ = forceClearAuthState;
    }

    return () => {
      if (typeof global !== 'undefined') {
        delete (global as any).__RESET_AUTH_STATE__;
      }
    };
  }, []);

  // Clear auth state when loading app
  useEffect(() => {
    const clearStaleAuthState = async () => {
      try {
        // Check last auth check timestamp
        const lastAuthCheck = await AsyncStorage.getItem('@app:last_auth_check');
        const now = Date.now();

        if (lastAuthCheck) {
          const elapsed = now - parseInt(lastAuthCheck);
          // If more than 1 hour since last check, clear auth state
          if (elapsed > 60 * 60 * 1000) {
            console.log('[_layout]Clearing stale auth state');
            await AsyncStorage.removeItem('@app:user_role');
            await AsyncStorage.removeItem('@app:profile_id');
            await AsyncStorage.removeItem('@app:otp_verification_in_progress');
            await AsyncStorage.removeItem('@app:verification_timestamp');
            await AsyncStorage.removeItem('@app:verifying_otp_in_progress');
          }
        }

        // Set current timestamp
        await AsyncStorage.setItem('@app:last_auth_check', now.toString());
      } catch (err) {
        console.log('[_layout] Error clearing stale auth state:', err);
      }
    };

    clearStaleAuthState();
  }, []);

  // Check for OTP verification in progress
  useEffect(() => {
    async function checkOtpVerification() {
      try {
        const otpInProgress = await AsyncStorage.getItem('@app:otp_verification_in_progress');
        setIsVerifyingOtp(otpInProgress === 'true');

        // Clear old verification flags if they've been around for too long (15+ min)
        const verificationTimestamp = await AsyncStorage.getItem('@app:verification_timestamp');
        if (verificationTimestamp) {
          const elapsed = Date.now() - parseInt(verificationTimestamp, 10);
          if (elapsed > 15 * 60 * 1000) { // 15 minutes
            console.log('[_layout]Clearing stale verification flags');
            await AsyncStorage.removeItem('@app:otp_verification_in_progress');
            await AsyncStorage.removeItem('@app:verification_timestamp');
            setIsVerifyingOtp(false);
          }
        } else if (otpInProgress === 'true') {
          // Set timestamp for new verification
          await AsyncStorage.setItem('@app:verification_timestamp', Date.now().toString());
        }
      } catch (err) {
        console.log('[_layout] Error checking OTP verification state:', err);
      }
    }
    checkOtpVerification();
  }, []);

  // More reliable path checking (check for login OR verify-otp screens)
  const isAuthScreen = React.useMemo(() => {
    ///const currentPath = segments.join('/');

    // Check for auth routes by segment
    if (segments.length > 0 && segments[0] === '(auth)') {
      return true; // All screens under (auth) are auth screens
    }

    return false;
  }, [segments]);

  // Navigation effect with MUCH simpler logic
  useEffect(() => {
    if (loading || isVerifyingOtp || showingAlert || preventLoginRedirect.current) return;

    // Check if we're in an auth route
    const inAuthGroup = segments.length > 0 && segments[0] === '(auth)';
    // Safe way to get the current route path
    const currentPath = segments.join('/');
    // Simplify navigation checks
    const handleNavigation = async () => {
      try {


        if (!isAuthenticated) {
          // If not authenticated and not in allowed auth screens
          if (!inAuthGroup ||
              !(currentPath.includes('language-select') ||
                currentPath.includes('login.v2') ||
                currentPath.includes('register'))) {
            // Redirect to the language selection screen
            router.replace('/(auth)/language-select');
          }
        } else if (isAuthenticated && inAuthGroup) {
          // Redirect to the home page if authenticated and in the auth group
          router.replace('/');
        }


        // Check if we're on root or index screen and authenticated - let them stay
        /* eslint-disable @typescript-eslint/no-unnecessary-condition */
        /* eslint-disable @typescript-eslint/no-unsafe-member-access */
        /* eslint-disable @typescript-eslint/no-unsafe-call */
        const isRootRoute = !segments || (segments.length as number) === 0;
        const isIndexRoute = Array.isArray(segments) && (segments.length as number) === 1 && segments[0] === 'index';
        /* eslint-enable @typescript-eslint/no-unnecessary-condition */
        /* eslint-enable @typescript-eslint/no-unsafe-member-access */
        /* eslint-enable @typescript-eslint/no-unsafe-call */
        const isRootOrIndex = isRootRoute || isIndexRoute;

        if (isRootOrIndex && isAuthenticated) {
          console.log('[_layout]On index screen or root and authenticated, staying here');
          return;
        }

        // If authenticated and on auth screen, go to home
        if (isAuthenticated && inAuthGroup) {
          console.log('[_layout]Authenticated but on auth screen, redirecting to home');
          router.replace('/');
          return;
        }
      } catch (error) {
        console.log('[_layout] Navigation error:', error);
      }
    };

    // Run navigation logic once
    handleNavigation();
  }, [isAuthenticated, loading, segments, router, isVerifyingOtp, showingAlert, isAuthScreen, preventLoginRedirect]);


  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }}>
        <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
      </View>
    );
  }

  return <>{children}</>;
}

function RootLayoutNav() {
  // Simple reset tap handler for emergencies
  const [tapCount, setTapCount] = useState(0);
  const router = useRouter();

  // Reset function (simplified version)
  const resetApp = async () => {
    await AsyncStorage.clear();
    await supabase.auth.signOut();
    router.replace('/(auth)/language-select');
  };

  useEffect(() => {
    // Reset tap count after 3 seconds
    if (tapCount > 0) {
      const timer = setTimeout(() => setTapCount(0), 3000);
      return () => clearTimeout(timer);
    }

    // Show alert after 5 taps
    if (tapCount >= 5) {
      Alert.alert(
        'Emergency Reset',
        'Would you like to reset all authentication state?',
        [
          { text: 'Cancel', style: 'cancel', onPress: () => setTapCount(0) },
          { text: 'Reset', onPress: resetApp }
        ]
      );
      setTapCount(0);
    }
  }, [tapCount, router]);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <StatusBarManager />
      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
        {/* Reference the group */}
        <Stack.Screen name="(auth)" options={{ headerShown: false }} />

        {/* Authenticated routes */}
        <Stack.Screen name="(app)" options={{ headerShown: false }} />
        <Stack.Screen name="index" options={{ headerShown: false }} />
        <Stack.Screen name="profile" options={{ headerShown: false }} />
        <Stack.Screen name="orders" options={{ headerShown: false }} />
        <Stack.Screen name="order-details/[orderId]" options={{ headerShown: false }} />
        <Stack.Screen name="dumpster-selection/index" options={{ headerShown: false }} />
        <Stack.Screen name="dumpster-selection/[dumpsterId]" options={{ headerShown: false }} />
        <Stack.Screen name="all-dumpsters" options={{ presentation: 'modal', headerShown: false }} />
        <Stack.Screen name="onboarding/OnboardingScreen" options={{ presentation: 'modal',headerShown: false }} />
        <Stack.Screen name="home-v5" options={{ headerShown: false }} />
        <Stack.Screen name="dumpster-details" options={{ headerShown: true }} />

        {/* Debug routes */}
        {process.env.NODE_ENV === 'development' && (
          <Stack.Screen name="debug" options={{ headerShown: false }} />
        )}
      </Stack>
    </GestureHandlerRootView>
  );
}

export default function RootLayout() {
  const router = useRouter();
  const segments = useSegments();
  const pathname = usePathname();
  const preventLoginRedirect = useRef(false);
  const [showingAlert, setShowingAlert] = useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [appError, setAppError] = useState<string | null>(null);
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);
  const realtimeChannelRef = useRef<RealtimeChannel | null>(null);

  // Check if the OTP rate limiting is applied
  const checkAndApplyRateLimitingMigration = async () => {
    try {
      // Check if rate limiting has been applied
      const { data, error } = await supabase.rpc('check_otp_rate_limit', {
        phone_number: 'test'
      });

      if (error && error.message.includes('function "check_otp_rate_limit" does not exist')) {
        console.log('[_layout]OTP rate limiting not applied yet. Please run the migration 004_otp_rate_limiting.sql');

        // We'll continue anyway but log this issue
        console.warn('[_layout]OTP rate limiting not applied. Some features may not work correctly.');
      } else {
        console.log('[_layout]OTP rate limiting is applied.');
      }
    } catch (err) {
      // Non-fatal error, continue with app startup
      console.warn('[_layout]Could not check OTP rate limiting:', err);
    }
  };

  useEffect(() => {
    async function prepare() {
      try {
        console.log('[_layout] Starting app preparation...');

        // --- RTL Reload Check ---
        // Check retry count first
        const retryCountStr = await AsyncStorage.getItem(RTL_RETRY_COUNT_KEY);
        const retryCount = retryCountStr ? parseInt(retryCountStr) : 0;

        // Check if a reload is pending from a previous attempt
        const needsForcedRestart = await AsyncStorage.getItem(FORCE_RESTART_KEY);
        if (needsForcedRestart === 'true') {
          console.log('[_layout] FORCE_RESTART_KEY is set. Checking retry count...');

          // Check if we're in a retry loop
          if (retryCount >= MAX_RTL_RETRIES) {
            console.log(`[_layout] Reached maximum RTL reload attempts (${MAX_RTL_RETRIES}). Continuing with current settings.`);
            // Clear flags and proceed without reloading
            await AsyncStorage.removeItem(FORCE_RESTART_KEY);
            await AsyncStorage.removeItem(RTL_RETRY_COUNT_KEY);
          } else {
            // Still have retries left
            console.log(`[_layout] RTL reload attempt ${retryCount+1}/${MAX_RTL_RETRIES}`);
            // Clear the restart flag (i18n will handle incrementing the counter)
            await AsyncStorage.removeItem(FORCE_RESTART_KEY);
            // Increment and store retry count
            await AsyncStorage.setItem(RTL_RETRY_COUNT_KEY, (retryCount+1).toString());
            // Only use Updates.reloadAsync in production builds
            if (!__DEV__ && Updates.isEnabled) {
              await Updates.reloadAsync();
            } else {
              // In development, just reset the app state
              console.log('[_layout] Development mode: Skipping Updates.reloadAsync, resetting app state');
              setIsReady(false);
              setTimeout(() => setIsReady(true), 100);
            }
            return; // Exit early
          }
        }
        // --- End RTL Reload Check ---

        console.log('[_layout] No forced restart needed. Initializing language settings...');

        // Initialize language and RTL settings
        // This function now returns true if it detected an RTL mismatch and applied changes
        const reloadNeeded = await initializeLanguageSettings();

        // If initialization indicated a reload is needed due to RTL changes
        if (reloadNeeded) {
          console.log('[_layout] initializeLanguageSettings indicated a reload is needed.');

          // Check retry count again
          const currentRetryStr = await AsyncStorage.getItem(RTL_RETRY_COUNT_KEY);
          const currentRetry = currentRetryStr ? parseInt(currentRetryStr) : 0;

          if (currentRetry >= MAX_RTL_RETRIES) {
            console.log(`[_layout] Already reached maximum RTL retries (${MAX_RTL_RETRIES}). Continuing without reload.`);
            // Clear flags and continue
            await AsyncStorage.removeItem(FORCE_RESTART_KEY);
            await AsyncStorage.removeItem(RTL_RETRY_COUNT_KEY);
          } else {
            // Set counter and reload
            console.log(`[_layout] Triggering RTL reload (attempt ${currentRetry+1}/${MAX_RTL_RETRIES})`);
            await AsyncStorage.setItem(RTL_RETRY_COUNT_KEY, (currentRetry+1).toString());
            // Keep FORCE_RESTART_KEY set - initializeLanguageSettings already did this
            // Only use Updates.reloadAsync in production builds
            if (!__DEV__ && Updates.isEnabled) {
              await Updates.reloadAsync();
            } else {
              // In development, just reset the app state
              console.log('[_layout] Development mode: Skipping Updates.reloadAsync, resetting app state');
              setIsReady(false);
              setTimeout(() => setIsReady(true), 100);
            }
            return; // Exit early
          }
        }

        // Continue with normal app setup...
        console.log('[_layout] Language settings initialized without needing immediate reload.');

        // Ensure all translations are loaded
        await ensureTranslationsLoaded();

        // Check rate limiting migration (keep this)
        await checkAndApplyRateLimitingMigration();

        // Register background tasks
        if (registerBackgroundFetch) {
          console.log('[_layout] Registering background fetch task');
          await registerBackgroundFetch();
        }

        console.log('[_layout] App preparation complete.');
        setIsReady(true);

      } catch (error) {
        console.error('[_layout] Critical error during app preparation:', error);
        // Clear the restart flags to prevent potential error loops
        await AsyncStorage.removeItem(FORCE_RESTART_KEY);
        await AsyncStorage.removeItem(RTL_RETRY_COUNT_KEY);
        setAppError(error instanceof Error ? error.message : 'An error occurred during startup');
      }
    }

    prepare();
  }, []);

  // Set up notifications
  useEffect(() => {
    // Configure notification behavior
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });

    // Request permissions
    const requestNotificationPermissions = async () => {
      if (Platform.OS === 'ios') {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        console.log('[_layout] Notification permission status:', existingStatus);

        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          console.log('[_layout] New notification permission status:', status);
          if (status !== 'granted') {
            Alert.alert('Notifications', 'You need to enable notifications to receive updates about your orders in real time.');
          }
        }
      }
    };

    requestNotificationPermissions();

    // Set up notification listeners and handle navigation
    const cleanupListeners = setupNotificationListeners();

    // Check for pending navigation from notification tap
    const checkPendingNavigation = async () => {
      try {
        // Get navigation data
        const pendingOrderId = await AsyncStorage.getItem('pendingOrderNavigation');
        const notificationTriggered = await AsyncStorage.getItem('notificationTriggeredNavigation');
        const timestampStr = await AsyncStorage.getItem('pendingNavigationTimestamp');

        // Only navigate if we have an order ID
        if (pendingOrderId) {
          console.log('[_layout] Found pending navigation to order:', pendingOrderId);

          // Check if the navigation request is recent (less than 5 minutes old)
          if (timestampStr) {
            const timestamp = parseInt(timestampStr);
            const now = Date.now();
            const isRecent = now - timestamp < 5 * 60 * 1000; // 5 minutes

            if (!isRecent) {
              console.log('[_layout] Pending navigation is too old, ignoring');
              // Clean up old navigation data
              await AsyncStorage.multiRemove([
                'pendingOrderNavigation',
                'pendingNavigationTimestamp',
                'notificationTriggeredNavigation'
              ]);
              return;
            }
          }

          // Clear navigation data immediately to prevent multiple navigations
          await AsyncStorage.multiRemove([
            'pendingOrderNavigation',
            'pendingNavigationTimestamp',
            'notificationTriggeredNavigation'
          ]);

          // Navigate to the order details after a short delay
          // This delay helps ensure the app is fully loaded
          setTimeout(() => {
            console.log('[_layout] Navigating to order details:', pendingOrderId);
            router.push(`/orders/${pendingOrderId}`);
          }, 1000);
        }
      } catch (error) {
        console.error('[_layout] Error checking pending navigation:', error);
      }
    };

    // Run the navigation check
    checkPendingNavigation();

    return () => {
      cleanupListeners();
    };
  }, [router]);

  // Setup AppState listener to handle app coming back to foreground
  useEffect(() => {
    // This helps handle notification taps when app is in background
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'active') {
        console.log('[APPSTATE] App has come to the foreground, checking for pending navigation');
        checkPendingNotificationNavigation();
      }
    });

    // Function to check pending navigation (reused from notification setup)
    const checkPendingNotificationNavigation = async () => {
      try {
        const pendingOrderId = await AsyncStorage.getItem('pendingOrderNavigation');
        const notificationTriggered = await AsyncStorage.getItem('notificationTriggeredNavigation');

        if (pendingOrderId && notificationTriggered === 'true') {
          console.log('[APPSTATE] Found pending notification navigation to order:', pendingOrderId);

          // Clear navigation data to prevent multiple navigations
          await AsyncStorage.multiRemove([
            'pendingOrderNavigation',
            'pendingNavigationTimestamp',
            'notificationTriggeredNavigation'
          ]);

          // Navigate to order details
          router.push(`/orders/${pendingOrderId}`);
        }
      } catch (error) {
        console.error('[APPSTATE] Error checking pending navigation:', error);
      }
    };

    return () => {
      subscription.remove();
    };
  }, [router]);

  // Effect for global realtime updates
  useEffect(() => {
    // Only set up the global realtime listener when the app is ready
    if (!isReady) return;

    console.log('[GLOBAL] Setting up global realtime listener');

    // Create a global realtime channel
    const channel = supabase.channel('global-app-updates', {
      config: { broadcast: { self: true } }
    });

    // Store the channel reference
    realtimeChannelRef.current = channel;

    // Listen for order status changes
    channel
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'order_status_history'
      }, (payload) => {
        console.log('[GLOBAL] Received order status change:', payload);

        // Force refresh any relevant queries
        queryClient.invalidateQueries({ queryKey: ['orders'] });

        // If we have specific order ID, invalidate that query too
        if (payload.new && 'order_id' in payload.new) {
          const orderId = payload.new.order_id;
          queryClient.invalidateQueries({ queryKey: ['order', orderId] });

          // Show a notification if the app is in the foreground
          const title = 'Order Status Update';
          const body = `Your order #${orderId.slice(0, 4)} status has changed`;

          // Trigger notification using our helper - don't use dynamic import
          const { sendRealtimeNotification } = require('@/hooks/useOrderRealtime');
          if (sendRealtimeNotification) {
            sendRealtimeNotification(title, body, {
              type: 'order_update',
              orderId: orderId
            });
          }
        }
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'orders'
      }, (payload) => {
        console.log('[GLOBAL] Received order change:', payload);

        // Force refresh orders data
        queryClient.invalidateQueries({ queryKey: ['orders'] });

        // Refresh specific order if we have the ID
        if (payload.new && 'id' in payload.new) {
          queryClient.invalidateQueries({ queryKey: ['order', payload.new.id] });
        }
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'driver_assignments'
      }, (payload) => {
        console.log('[GLOBAL] Received driver assignment change:', payload);

        // Force refresh orders data
        queryClient.invalidateQueries({ queryKey: ['orders'] });

        // Refresh specific order if we have the order_id
        if (payload.new && 'order_id' in payload.new) {
          queryClient.invalidateQueries({ queryKey: ['order', payload.new.order_id] });
        }
      })
      .subscribe((status) => {
        console.log(`[GLOBAL] Global subscription status: ${status}`);
      });

    // Clean up function
    return () => {
      console.log('[GLOBAL] Cleaning up global realtime listener');
      if (realtimeChannelRef.current) {
        supabase.removeChannel(realtimeChannelRef.current);
        realtimeChannelRef.current = null;
      }
    };
  }, [isReady, queryClient]);

  if (!isReady) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (appError) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
        <Text style={{ textAlign: 'center', marginBottom: 20, fontSize: 16 }}>
          {appError}
        </Text>
        <Button
          title="Retry"
          onPress={() => {
            setAppError(null);
            // Only use Updates.reloadAsync in production builds
            if (!__DEV__ && Updates.isEnabled) {
              Updates.reloadAsync().catch(() => {
                // If reload fails, just refresh the page
                setIsReady(false);
                setTimeout(() => setIsReady(true), 100);
              });
            } else {
              // In development, just refresh the page
              setIsReady(false);
              setTimeout(() => setIsReady(true), 100);
            }
          }}
        />
      </View>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        <RTLProvider>
          <ThemeProvider>
            <AuthProvider>
              <MigrationProvider>
                <AuthenticationGuard>
                  <RootLayoutNav />
                </AuthenticationGuard>
              </MigrationProvider>
            </AuthProvider>
          </ThemeProvider>
        </RTLProvider>
      </I18nextProvider>
    </QueryClientProvider>
  );
}